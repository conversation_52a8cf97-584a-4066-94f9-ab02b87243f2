[api_wins] [2025-08-07 21:12:45] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(23) "api\autodesk\customers\"\n  ["function_call"]: string(27) "api\autodesk\customers\view"\n  ["path_parts"]: array(2) {\n    [0]: string(3) "api"\n    [1]: string(4) "view"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 139\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 132\n         <strong>Arguments:</strong> \n         0: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resou...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resou...\n\n----------------------------------------------------------------------------\n-->\n
