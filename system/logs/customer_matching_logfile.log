[customer_matching] [2025-08-07 21:12:45] [customers.api.php:160] Customer matching - Found 319 CSV entries to check
[customer_matching] [2025-08-07 21:12:45] [customers.api.php:161] Customer data: email=<EMAIL>, name=EDM-London
[customer_matching] [2025-08-07 21:12:46] [customers.api.php:182] Comparing emails: customer='<EMAIL>' vs entry[email_address]='<EMAIL>'
[customer_matching] [2025-08-07 21:12:46] [customers.api.php:212] Comparing companies: customer='edmlondon' vs entry[company_name]='test company' (similarity: 28.571428571429%)
[customer_matching] [2025-08-07 21:12:46] [customers.api.php:182] Comparing emails: customer='<EMAIL>' vs entry[email_address]='<EMAIL>'
[customer_matching] [2025-08-07 21:12:46] [customers.api.php:212] Comparing companies: customer='edmlondon' vs entry[company_name]='sample' (similarity: 13.333333333333%)
[customer_matching] [2025-08-07 21:12:46] [customers.api.php:182] Comparing emails: customer='<EMAIL>' vs entry[email_address]='<EMAIL>'
[customer_matching] [2025-08-07 21:12:46] [customers.api.php:212] Comparing companies: customer='edmlondon' vs entry[company_name]='demo industries' (similarity: 41.666666666667%)
[customer_matching] [2025-08-07 21:12:46] [customers.api.php:237] Final result: Found 0 matching CSV subscriptions
