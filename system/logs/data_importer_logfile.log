[data_importer] [2025-07-31 09:07:41] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-07-31 09:07:41] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => JONATHAN LEES ARCHITECTS LLP\n    [:up_name] => J<PERSON><PERSON><PERSON><PERSON> LEES ARCHITECTS LLP\n)\n
[data_importer] [2025-07-31 09:07:41] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-07-31 09:07:41] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-07-31 09:07:41] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-07-31 09:07:41] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-07-31 09:07:41] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => JONATHAN LEES ARCHITECTS LLP\n    [:up_name] => JONATHAN LEES ARCHITECTS LLP\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => Unit D Baptist Mills Court\n    [:up_address1] => Unit D Baptist Mills Court\n    [:city] => Bristol\n    [:up_city] => Bristol\n    [:postal_code] => BS5 0FJ\n    [:up_postal_code] => BS5 0FJ\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Architecture Services\n    [:up_parent_industry_segment] => Architecture Services\n    [:primary_admin_first_name] => Rebecca\n    [:up_primary_admin_first_name] => Rebecca\n    [:primary_admin_last_name] => Blackwell\n    [:up_primary_admin_last_name] => Blackwell\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 8222214\n    [:up_team_id] => 8222214\n    [:team_name] => Jonathan Lees Architects - 2214\n    [:up_team_name] => Jonathan Lees Architects - 2214\n    [:first_name] => Rebecca\n    [:up_first_name] => Rebecca\n    [:last_name] => Blackwell\n    [:up_last_name] => Blackwell\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => \n    [:up_do_not_email] => \n    [:do_not_mail] => \n    [:up_do_not_mail] => \n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-07-31 09:07:41] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-07-31 09:07:41] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, opportunityNumber = :opportunityNumber, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, opportunityNumber = :up_opportunityNumber, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-07-31 09:07:41] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 56927469919167\n    [:subscriptionReferenceNumber] => 566-85727259\n    [:up_subscriptionReferenceNumber] => 566-85727259\n    [:quantity] => 2\n    [:up_quantity] => 2\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-09-25\n    [:up_startDate] => 2019-09-25\n    [:endDate] => 2026-07-16\n    [:up_endDate] => 2026-07-16\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => OFF\n    [:up_autoRenew] => OFF\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:opportunityNumber] => **********\n    [:up_opportunityNumber] => **********\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => LOC\n    [:up_paymentMethod] => LOC\n    [:endCustomer_id] => 275355\n    [:up_endCustomer_id] => 275355\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 4223\n    [:up_soldTo_id] => 4223\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => EAST STAFFORDSHIRE BOROUGH COUNCIL\n    [:up_name] => EAST STAFFORDSHIRE BOROUGH COUNCIL\n    [:address1] => Town Hall\n    [:up_address1] => Town Hall\n    [:city] => Burton-on-trent\n    [:up_city] => Burton-on-trent\n    [:postal_code] => DE14 2EB\n    [:up_postal_code] => DE14 2EB\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => STAFFORDSHIRE\n    [:up_state_province] => STAFFORDSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Ian\n    [:up_first_name] => Ian\n    [:last_name] => Welby\n    [:up_last_name] => Welby\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-973308\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-07-31T14:23:39+01:00\n    [:up_quote_created_time] => 2025-07-31T14:23:39+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 11076\n    [:up_end_customer] => 11076\n    [:quote_contact] => 275359\n    [:up_quote_contact] => 275359\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-07-31 13:23:45] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-01 00:11:26] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-08-01 00:11:26] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => CHEMOXY\n    [:up_name] => CHEMOXY\n)\n
[data_importer] [2025-08-01 00:11:26] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-08-01 00:11:26] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-01 00:11:26] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-08-01 00:11:26] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, state_province = :state_province, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, state_province = :up_state_province, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-08-01 00:11:26] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => CHEMOXY\n    [:up_name] => CHEMOXY\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => Haverton Hill Road\n    [:up_address1] => Haverton Hill Road\n    [:address2] => Road\n    [:up_address2] => Road\n    [:city] => Billingham\n    [:up_city] => Billingham\n    [:state_province] => CLEVELAND\n    [:up_state_province] => CLEVELAND\n    [:postal_code] => TS23 1PY\n    [:up_postal_code] => TS23 1PY\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => MFG\n    [:up_parent_industry_group] => MFG\n    [:parent_industry_segment] => Process Manufacturing\n    [:up_parent_industry_segment] => Process Manufacturing\n    [:primary_admin_first_name] => Peter\n    [:up_primary_admin_first_name] => Peter\n    [:primary_admin_last_name] => McArthur\n    [:up_primary_admin_last_name] => McArthur\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 7050254\n    [:up_team_id] => 7050254\n    [:team_name] => David Pearson - 0254\n    [:up_team_name] => David Pearson - 0254\n    [:first_name] => Peter\n    [:up_first_name] => Peter\n    [:last_name] => McArthur\n    [:up_last_name] => McArthur\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => 1\n    [:up_do_not_email] => 1\n    [:do_not_mail] => \n    [:up_do_not_mail] => \n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-01 00:11:26] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-08-01 00:11:26] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, opportunityNumber = :opportunityNumber, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, opportunityNumber = :up_opportunityNumber, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-08-01 00:11:26] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 55508693518307\n    [:subscriptionReferenceNumber] => 562-96510504\n    [:up_subscriptionReferenceNumber] => 562-96510504\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Suspended\n    [:up_status] => Suspended\n    [:startDate] => 2019-04-14\n    [:up_startDate] => 2019-04-14\n    [:endDate] => 2026-07-03\n    [:up_endDate] => 2026-07-03\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:opportunityNumber] => **********\n    [:up_opportunityNumber] => **********\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => LOC\n    [:up_paymentMethod] => LOC\n    [:endCustomer_id] => 275362\n    [:up_endCustomer_id] => 275362\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 319\n    [:up_soldTo_id] => 319\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-08-01 07:12:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-08-01 07:12:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TRITON CONSTRUCTION Ltd\n    [:up_name] => TRITON CONSTRUCTION Ltd\n)\n
[data_importer] [2025-08-01 07:12:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-08-01 07:12:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-01 07:12:50] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-08-01 07:12:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, state_province = :state_province, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, state_province = :up_state_province, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-08-01 07:12:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => TRITON CONSTRUCTION Ltd\n    [:up_name] => TRITON CONSTRUCTION Ltd\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => Hare Park Mills Hare Park Lane\n    [:up_address1] => Hare Park Mills Hare Park Lane\n    [:city] => Liversedge\n    [:up_city] => Liversedge\n    [:state_province] => WEST YORKSHIRE\n    [:up_state_province] => WEST YORKSHIRE\n    [:postal_code] => WF15 8EP\n    [:up_postal_code] => WF15 8EP\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Construction Services\n    [:up_parent_industry_segment] => Construction Services\n    [:primary_admin_first_name] => Roger\n    [:up_primary_admin_first_name] => Roger\n    [:primary_admin_last_name] => Swift\n    [:up_primary_admin_last_name] => Swift\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 8242108\n    [:up_team_id] => 8242108\n    [:team_name] => Roger Swift - 2108\n    [:up_team_name] => Roger Swift - 2108\n    [:first_name] => Graig\n    [:up_first_name] => Graig\n    [:last_name] => Newsome\n    [:up_last_name] => Newsome\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => \n    [:up_do_not_email] => \n    [:do_not_mail] => \n    [:up_do_not_mail] => \n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-01 07:12:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-08-01 07:12:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, opportunityNumber = :opportunityNumber, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, opportunityNumber = :up_opportunityNumber, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-08-01 07:12:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 56942318578860\n    [:subscriptionReferenceNumber] => 564-51915786\n    [:up_subscriptionReferenceNumber] => 564-51915786\n    [:quantity] => 2\n    [:up_quantity] => 2\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-09-27\n    [:up_startDate] => 2019-09-27\n    [:endDate] => 2026-07-02\n    [:up_endDate] => 2026-07-02\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:opportunityNumber] => **********\n    [:up_opportunityNumber] => **********\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => LOC\n    [:up_paymentMethod] => LOC\n    [:endCustomer_id] => 275365\n    [:up_endCustomer_id] => 275365\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 8787\n    [:up_soldTo_id] => 8787\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Fraser\n    [:up_first_name] => Fraser\n    [:last_name] => Dixon\n    [:up_last_name] => Dixon\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => THUNDERBOLT & MAINTENANCE\n    [:up_name] => THUNDERBOLT & MAINTENANCE\n    [:address1] => Unit 2 Forum Road\n    [:up_address1] => Unit 2 Forum Road\n    [:city] => Nottingham\n    [:up_city] => Nottingham\n    [:postal_code] => NG5 9RW\n    [:up_postal_code] => NG5 9RW\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Stevenson\n    [:up_last_name] => Stevenson\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-975780\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-01T10:53:51+01:00\n    [:up_quote_created_time] => 2025-08-01T10:53:51+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 8608\n    [:up_end_customer] => 8608\n    [:quote_contact] => 275369\n    [:up_quote_contact] => 275369\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-14\n    [:up_start_date] => 2025-09-14\n    [:end_date] => 2026-09-13\n    [:up_end_date] => 2026-09-13\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 60007740489301\n    [:up_subscription_id] => 60007740489301\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-13\n    [:up_subscription_endDate] => 2025-09-13\n    [:quote_id] => 1818\n    [:up_quote_id] => 1818\n)\n
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-01 09:53:59] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Fraser\n    [:up_first_name] => Fraser\n    [:last_name] => Dixon\n    [:up_last_name] => Dixon\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => HH Adkins Contractors Ltd\n    [:up_name] => HH Adkins Contractors Ltd\n    [:address1] => Wyberton West Road\n    [:up_address1] => Wyberton West Road\n    [:city] => Boston\n    [:up_city] => Boston\n    [:postal_code] => PE21 7JU\n    [:up_postal_code] => PE21 7JU\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Martin\n    [:up_first_name] => Martin\n    [:last_name] => Pape\n    [:up_last_name] => Pape\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, quote_opportunity_number = :quote_opportunity_number, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, quote_opportunity_number = :up_quote_opportunity_number, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-975921\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-01T11:55:18+01:00\n    [:up_quote_created_time] => 2025-08-01T11:55:18+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:quote_opportunity_number] => A-********\n    [:up_quote_opportunity_number] => A-********\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 2460\n    [:up_total_list_amount] => 2460\n    [:total_net_amount] => 2337\n    [:up_total_net_amount] => 2337\n    [:total_amount] => 2804.4\n    [:up_total_amount] => 2804.4\n    [:total_discount] => 123\n    [:up_total_discount] => 123\n    [:estimated_tax] => 467.4\n    [:up_estimated_tax] => 467.4\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 7714\n    [:up_end_customer] => 7714\n    [:quote_contact] => 275373\n    [:up_quote_contact] => 275373\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-28\n    [:up_start_date] => 2025-08-28\n    [:end_date] => 2028-08-27\n    [:up_end_date] => 2028-08-27\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 1230\n    [:up_unit_srp] => 1230\n    [:extended_srp] => 1230\n    [:up_extended_srp] => 1230\n    [:discounts_applied] => 61.5\n    [:up_discounts_applied] => 61.5\n    [:end_user_price] => 1168.5\n    [:up_end_user_price] => 1168.5\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 56700593929570\n    [:up_subscription_id] => 56700593929570\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-08-27\n    [:up_subscription_endDate] => 2025-08-27\n    [:quote_id] => 1820\n    [:up_quote_id] => 1820\n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 2\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-30\n    [:up_start_date] => 2025-08-30\n    [:end_date] => 2028-08-29\n    [:up_end_date] => 2028-08-29\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 1230\n    [:up_unit_srp] => 1230\n    [:extended_srp] => 1230\n    [:up_extended_srp] => 1230\n    [:discounts_applied] => 61.5\n    [:up_discounts_applied] => 61.5\n    [:end_user_price] => 1168.5\n    [:up_end_user_price] => 1168.5\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 56716427656767\n    [:up_subscription_id] => 56716427656767\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-08-29\n    [:up_subscription_endDate] => 2025-08-29\n    [:quote_id] => 1820\n    [:up_quote_id] => 1820\n)\n
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-01 10:55:29] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Fraser\n    [:up_first_name] => Fraser\n    [:last_name] => Dixon\n    [:up_last_name] => Dixon\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Struco Ltd\n    [:up_name] => Struco Ltd\n    [:address1] => 71-75 Shelton Street\n    [:up_address1] => 71-75 Shelton Street\n    [:city] => London\n    [:up_city] => London\n    [:postal_code] => WC2H 9JQ\n    [:up_postal_code] => WC2H 9JQ\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => LONDON\n    [:up_state_province] => LONDON\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Dawid\n    [:up_first_name] => Dawid\n    [:last_name] => Powezka\n    [:up_last_name] => Powezka\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-976055\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-01T13:12:46+01:00\n    [:up_quote_created_time] => 2025-08-01T13:12:46+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 275376\n    [:up_end_customer] => 275376\n    [:quote_contact] => 275377\n    [:up_quote_contact] => 275377\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-08-01 12:12:53] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => EAST STAFFORDSHIRE BOROUGH COUNCIL\n    [:up_name] => EAST STAFFORDSHIRE BOROUGH COUNCIL\n    [:address1] => Town Hall\n    [:up_address1] => Town Hall\n    [:city] => Burton-on-trent\n    [:up_city] => Burton-on-trent\n    [:postal_code] => DE14 2EB\n    [:up_postal_code] => DE14 2EB\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => STAFFORDSHIRE\n    [:up_state_province] => STAFFORDSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Ian\n    [:up_first_name] => Ian\n    [:last_name] => Welby\n    [:up_last_name] => Welby\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-976151\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-01T13:52:53+01:00\n    [:up_quote_created_time] => 2025-08-01T13:52:53+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 11076\n    [:up_end_customer] => 11076\n    [:quote_contact] => 275359\n    [:up_quote_contact] => 275359\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-08-01 12:52:59] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Thermal Earth Ltd\n    [:up_name] => Thermal Earth Ltd\n    [:address1] => Unit B1 Capel Hendre Ind Est\n    [:up_address1] => Unit B1 Capel Hendre Ind Est\n    [:city] => Ammanford\n    [:up_city] => Ammanford\n    [:postal_code] => SA18 3SJ\n    [:up_postal_code] => SA18 3SJ\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Thermal\n    [:up_first_name] => Thermal\n    [:last_name] => Earth\n    [:up_last_name] => Earth\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-976696\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-01T14:56:21+01:00\n    [:up_quote_created_time] => 2025-08-01T14:56:21+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 6487\n    [:up_end_customer] => 6487\n    [:quote_contact] => 275385\n    [:up_quote_contact] => 275385\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-08-01 13:56:27] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-03 12:10:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-08-03 12:10:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => CHEMOXY\n    [:up_name] => CHEMOXY\n)\n
[data_importer] [2025-08-03 12:10:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-08-03 12:10:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-03 12:10:58] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-08-03 12:10:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, state_province = :state_province, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, state_province = :up_state_province, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-08-03 12:10:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => CHEMOXY\n    [:up_name] => CHEMOXY\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => Haverton Hill Road\n    [:up_address1] => Haverton Hill Road\n    [:address2] => Road\n    [:up_address2] => Road\n    [:city] => Billingham\n    [:up_city] => Billingham\n    [:state_province] => CLEVELAND\n    [:up_state_province] => CLEVELAND\n    [:postal_code] => TS23 1PY\n    [:up_postal_code] => TS23 1PY\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => MFG\n    [:up_parent_industry_group] => MFG\n    [:parent_industry_segment] => Process Manufacturing\n    [:up_parent_industry_segment] => Process Manufacturing\n    [:primary_admin_first_name] => Peter\n    [:up_primary_admin_first_name] => Peter\n    [:primary_admin_last_name] => McArthur\n    [:up_primary_admin_last_name] => McArthur\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 7050254\n    [:up_team_id] => 7050254\n    [:team_name] => David Pearson - 0254\n    [:up_team_name] => David Pearson - 0254\n    [:first_name] => Peter\n    [:up_first_name] => Peter\n    [:last_name] => McArthur\n    [:up_last_name] => McArthur\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => 1\n    [:up_do_not_email] => 1\n    [:do_not_mail] => \n    [:up_do_not_mail] => \n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-03 12:10:58] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-08-03 12:10:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, subscriptionReferenceNumber = :subscriptionReferenceNumber, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, opportunityNumber = :opportunityNumber, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), subscriptionReferenceNumber = :up_subscriptionReferenceNumber, quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, opportunityNumber = :up_opportunityNumber, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-08-03 12:10:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 55508693518307\n    [:subscriptionReferenceNumber] => 562-96510504\n    [:up_subscriptionReferenceNumber] => 562-96510504\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2019-04-14\n    [:up_startDate] => 2019-04-14\n    [:endDate] => 2026-07-03\n    [:up_endDate] => 2026-07-03\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:opportunityNumber] => **********\n    [:up_opportunityNumber] => **********\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => LOC\n    [:up_paymentMethod] => LOC\n    [:endCustomer_id] => 275388\n    [:up_endCustomer_id] => 275388\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 319\n    [:up_soldTo_id] => 319\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => McCarthy Design Solutions\n    [:up_name] => McCarthy Design Solutions\n    [:address1] => 40 Cleveland Gardens\n    [:up_address1] => 40 Cleveland Gardens\n    [:city] => Newcastle Upon Tyne\n    [:up_city] => Newcastle Upon Tyne\n    [:postal_code] => NE7 7QH\n    [:up_postal_code] => NE7 7QH\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => TYNE AND WEAR\n    [:up_state_province] => TYNE AND WEAR\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Carl\n    [:up_first_name] => Carl\n    [:last_name] => McCarthy\n    [:up_last_name] => McCarthy\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979434\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T08:56:14+01:00\n    [:up_quote_created_time] => 2025-08-04T08:56:14+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 535\n    [:up_total_list_amount] => 535\n    [:total_net_amount] => 535\n    [:up_total_net_amount] => 535\n    [:total_amount] => 642\n    [:up_total_amount] => 642\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 107\n    [:up_estimated_tax] => 107\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 7280\n    [:up_end_customer] => 7280\n    [:quote_contact] => 275392\n    [:up_quote_contact] => 275392\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-04\n    [:up_start_date] => 2025-09-04\n    [:end_date] => 2026-09-03\n    [:up_end_date] => 2026-09-03\n    [:offering_id] => OD-000280\n    [:up_offering_id] => OD-000280\n    [:offering_code] => RVTLTS\n    [:up_offering_code] => RVTLTS\n    [:offering_name] => AutoCAD Revit LT Suite\n    [:up_offering_name] => AutoCAD Revit LT Suite\n    [:marketing_name] => AutoCAD Revit LT Suite\n    [:up_marketing_name] => AutoCAD Revit LT Suite\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 535\n    [:up_unit_srp] => 535\n    [:extended_srp] => 535\n    [:up_extended_srp] => 535\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 535\n    [:up_end_user_price] => 535\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 69381769773020\n    [:up_subscription_id] => 69381769773020\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-03\n    [:up_subscription_endDate] => 2025-09-03\n    [:quote_id] => 1843\n    [:up_quote_id] => 1843\n)\n
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 07:56:21] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => apdesign\n    [:up_name] => apdesign\n    [:address1] => 9a Wordsworth Avenue\n    [:up_address1] => 9a Wordsworth Avenue\n    [:city] => Greenford\n    [:up_city] => Greenford\n    [:postal_code] => UB6 9AA\n    [:up_postal_code] => UB6 9AA\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => MIDDLESEX\n    [:up_state_province] => MIDDLESEX\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Andrew\n    [:up_first_name] => Andrew\n    [:last_name] => Porter\n    [:up_last_name] => Porter\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979447\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T08:58:26+01:00\n    [:up_quote_created_time] => 2025-08-04T08:58:26+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 670\n    [:up_end_customer] => 670\n    [:quote_contact] => 18686\n    [:up_quote_contact] => 18686\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-08-04 07:58:32] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => apdesign\n    [:up_name] => apdesign\n    [:address1] => 9a Wordsworth Avenue\n    [:up_address1] => 9a Wordsworth Avenue\n    [:city] => Greenford\n    [:up_city] => Greenford\n    [:postal_code] => UB6 9AA\n    [:up_postal_code] => UB6 9AA\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => MIDDLESEX\n    [:up_state_province] => MIDDLESEX\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Andrew\n    [:up_first_name] => Andrew\n    [:last_name] => Porter\n    [:up_last_name] => Porter\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979451\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T08:58:54+01:00\n    [:up_quote_created_time] => 2025-08-04T08:58:54+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 492\n    [:up_total_amount] => 492\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 82\n    [:up_estimated_tax] => 82\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 670\n    [:up_end_customer] => 670\n    [:quote_contact] => 18686\n    [:up_quote_contact] => 18686\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-04\n    [:up_start_date] => 2025-09-04\n    [:end_date] => 2026-09-03\n    [:up_end_date] => 2026-09-03\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 69383163408225\n    [:up_subscription_id] => 69383163408225\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-03\n    [:up_subscription_endDate] => 2025-09-03\n    [:quote_id] => 1846\n    [:up_quote_id] => 1846\n)\n
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 07:59:01] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Concept Eight Architects\n    [:up_name] => Concept Eight Architects\n    [:address1] => Pine Grove\n    [:up_address1] => Pine Grove\n    [:city] => Weybridge\n    [:up_city] => Weybridge\n    [:postal_code] => KT13 9BD\n    [:up_postal_code] => KT13 9BD\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => SURREY\n    [:up_state_province] => SURREY\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Mufajel\n    [:up_first_name] => Mufajel\n    [:last_name] => Chowdhury\n    [:up_last_name] => Chowdhury\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979458\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T08:59:55+01:00\n    [:up_quote_created_time] => 2025-08-04T08:59:55+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 395\n    [:up_end_customer] => 395\n    [:quote_contact] => 15838\n    [:up_quote_contact] => 15838\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-20\n    [:up_start_date] => 2025-08-20\n    [:end_date] => 2026-08-19\n    [:up_end_date] => 2026-08-19\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 62945623792724\n    [:up_subscription_id] => 62945623792724\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-08-19\n    [:up_subscription_endDate] => 2025-08-19\n    [:quote_id] => 1848\n    [:up_quote_id] => 1848\n)\n
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 08:00:01] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Cornerstone Projects Ltd\n    [:up_name] => Cornerstone Projects Ltd\n    [:address1] => 16 Forest Road\n    [:up_address1] => 16 Forest Road\n    [:address2] => Meols\n    [:up_address2] => Meols\n    [:city] => Wirral\n    [:up_city] => Wirral\n    [:postal_code] => CH47 6AU\n    [:up_postal_code] => CH47 6AU\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Duncan\n    [:up_first_name] => Duncan\n    [:last_name] => Phillips\n    [:up_last_name] => Phillips\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979487\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T09:04:19+01:00\n    [:up_quote_created_time] => 2025-08-04T09:04:19+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 2460\n    [:up_total_list_amount] => 2460\n    [:total_net_amount] => 2337\n    [:up_total_net_amount] => 2337\n    [:total_amount] => 2804.4\n    [:up_total_amount] => 2804.4\n    [:total_discount] => 123\n    [:up_total_discount] => 123\n    [:estimated_tax] => 467.4\n    [:up_estimated_tax] => 467.4\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1055\n    [:up_end_customer] => 1055\n    [:quote_contact] => 275408\n    [:up_quote_contact] => 275408\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 2\n    [:up_quantity] => 2\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-10-25\n    [:up_start_date] => 2025-10-25\n    [:end_date] => 2028-10-24\n    [:up_end_date] => 2028-10-24\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 1230\n    [:up_unit_srp] => 1230\n    [:extended_srp] => 2460\n    [:up_extended_srp] => 2460\n    [:discounts_applied] => 123\n    [:up_discounts_applied] => 123\n    [:end_user_price] => 2337\n    [:up_end_user_price] => 2337\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 57199375151109\n    [:up_subscription_id] => 57199375151109\n    [:subscription_quantity] => 2\n    [:up_subscription_quantity] => 2\n    [:subscription_endDate] => 2025-10-24\n    [:up_subscription_endDate] => 2025-10-24\n    [:quote_id] => 1850\n    [:up_quote_id] => 1850\n)\n
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 08:04:25] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Fraser\n    [:up_first_name] => Fraser\n    [:last_name] => Dixon\n    [:up_last_name] => Dixon\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => SeAH Wind Ltd\n    [:up_name] => SeAH Wind Ltd\n    [:address1] => Stephenson House High Force Road\n    [:up_address1] => Stephenson House High Force Road\n    [:address2] => Riverside Park Industrial Estate\n    [:up_address2] => Riverside Park Industrial Estate\n    [:city] => Middlesbrough\n    [:up_city] => Middlesbrough\n    [:postal_code] => TS2 1RH\n    [:up_postal_code] => TS2 1RH\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => CLEVELAND\n    [:up_state_province] => CLEVELAND\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Caitlan\n    [:up_first_name] => Caitlan\n    [:last_name] => Scorer\n    [:up_last_name] => Scorer\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, quote_opportunity_number = :quote_opportunity_number, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, quote_opportunity_number = :up_quote_opportunity_number, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979508\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T09:07:41+01:00\n    [:up_quote_created_time] => 2025-08-04T09:07:41+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:quote_opportunity_number] => A-********\n    [:up_quote_opportunity_number] => A-********\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 376\n    [:up_end_customer] => 376\n    [:quote_contact] => 14358\n    [:up_quote_contact] => 14358\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-03\n    [:up_start_date] => 2025-09-03\n    [:end_date] => 2026-09-02\n    [:up_end_date] => 2026-09-02\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 72535766652664\n    [:up_subscription_id] => 72535766652664\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-02\n    [:up_subscription_endDate] => 2025-09-02\n    [:quote_id] => 1852\n    [:up_quote_id] => 1852\n)\n
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 08:07:47] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => THORNE RAINWATER SYSTEMS\n    [:up_name] => THORNE RAINWATER SYSTEMS\n    [:address1] => 1 Pontfaen\n    [:up_address1] => 1 Pontfaen\n    [:city] => Cardiff\n    [:up_city] => Cardiff\n    [:postal_code] => CF23 7DU\n    [:up_postal_code] => CF23 7DU\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Terry\n    [:up_first_name] => Terry\n    [:last_name] => Thorne\n    [:up_last_name] => Thorne\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979512\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T09:08:23+01:00\n    [:up_quote_created_time] => 2025-08-04T09:08:23+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 607\n    [:up_end_customer] => 607\n    [:quote_contact] => 18506\n    [:up_quote_contact] => 18506\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-11-03\n    [:up_start_date] => 2025-11-03\n    [:end_date] => 2026-11-02\n    [:up_end_date] => 2026-11-02\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 60441654937163\n    [:up_subscription_id] => 60441654937163\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-11-02\n    [:up_subscription_endDate] => 2025-11-02\n    [:quote_id] => 1853\n    [:up_quote_id] => 1853\n)\n
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 08:08:28] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Ground Condition Consultants L\n    [:up_name] => Ground Condition Consultants L\n    [:address1] => 10 Waldegrave Close\n    [:up_address1] => 10 Waldegrave Close\n    [:city] => Southampton\n    [:up_city] => Southampton\n    [:postal_code] => SO19 9RY\n    [:up_postal_code] => SO19 9RY\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Antony\n    [:up_first_name] => Antony\n    [:last_name] => Platt\n    [:up_last_name] => Platt\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979541\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T09:12:21+01:00\n    [:up_quote_created_time] => 2025-08-04T09:12:21+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 2058\n    [:up_end_customer] => 2058\n    [:quote_contact] => 275420\n    [:up_quote_contact] => 275420\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-11-02\n    [:up_start_date] => 2025-11-02\n    [:end_date] => 2026-11-01\n    [:up_end_date] => 2026-11-01\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 55544086141564\n    [:up_subscription_id] => 55544086141564\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-11-01\n    [:up_subscription_endDate] => 2025-11-01\n    [:quote_id] => 1855\n    [:up_quote_id] => 1855\n)\n
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 08:12:27] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => GJR ARCHITECTS Ltd\n    [:up_name] => GJR ARCHITECTS Ltd\n    [:address1] => Axehayes Farm 1 Park 7 The Studio\n    [:up_address1] => Axehayes Farm 1 Park 7 The Studio\n    [:address2] => Clyst St. Mary\n    [:up_address2] => Clyst St. Mary\n    [:city] => Exeter\n    [:up_city] => Exeter\n    [:postal_code] => EX5 1DP\n    [:up_postal_code] => EX5 1DP\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => DEVON\n    [:up_state_province] => DEVON\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Helen\n    [:up_first_name] => Helen\n    [:last_name] => Barlow\n    [:up_last_name] => Barlow\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979570\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T09:17:07+01:00\n    [:up_quote_created_time] => 2025-08-04T09:17:07+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 2595\n    [:up_end_customer] => 2595\n    [:quote_contact] => 14922\n    [:up_quote_contact] => 14922\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-08-04 08:17:13] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => GJR ARCHITECTS Ltd\n    [:up_name] => GJR ARCHITECTS Ltd\n    [:address1] => Axehayes Farm 1 Park 7 The Studio\n    [:up_address1] => Axehayes Farm 1 Park 7 The Studio\n    [:address2] => Clyst St. Mary\n    [:up_address2] => Clyst St. Mary\n    [:city] => Exeter\n    [:up_city] => Exeter\n    [:postal_code] => EX5 1DP\n    [:up_postal_code] => EX5 1DP\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => DEVON\n    [:up_state_province] => DEVON\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Helen\n    [:up_first_name] => Helen\n    [:last_name] => Barlow\n    [:up_last_name] => Barlow\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979595\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T09:21:16+01:00\n    [:up_quote_created_time] => 2025-08-04T09:21:16+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 535\n    [:up_total_list_amount] => 535\n    [:total_net_amount] => 535\n    [:up_total_net_amount] => 535\n    [:total_amount] => 535\n    [:up_total_amount] => 535\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 2595\n    [:up_end_customer] => 2595\n    [:quote_contact] => 14922\n    [:up_quote_contact] => 14922\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-02\n    [:up_start_date] => 2025-09-02\n    [:end_date] => 2026-09-01\n    [:up_end_date] => 2026-09-01\n    [:offering_id] => OD-000280\n    [:up_offering_id] => OD-000280\n    [:offering_code] => RVTLTS\n    [:up_offering_code] => RVTLTS\n    [:offering_name] => AutoCAD Revit LT Suite\n    [:up_offering_name] => AutoCAD Revit LT Suite\n    [:marketing_name] => AutoCAD Revit LT Suite\n    [:up_marketing_name] => AutoCAD Revit LT Suite\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 535\n    [:up_unit_srp] => 535\n    [:extended_srp] => 535\n    [:up_extended_srp] => 535\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 535\n    [:up_end_user_price] => 535\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 72527238809770\n    [:up_subscription_id] => 72527238809770\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-01\n    [:up_subscription_endDate] => 2025-09-01\n    [:quote_id] => 1858\n    [:up_quote_id] => 1858\n)\n
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 08:21:22] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => GJR ARCHITECTS Ltd\n    [:up_name] => GJR ARCHITECTS Ltd\n    [:address1] => Axehayes Farm 1 Park 7 The Studio\n    [:up_address1] => Axehayes Farm 1 Park 7 The Studio\n    [:address2] => Clyst St. Mary\n    [:up_address2] => Clyst St. Mary\n    [:city] => Exeter\n    [:up_city] => Exeter\n    [:postal_code] => EX5 1DP\n    [:up_postal_code] => EX5 1DP\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => DEVON\n    [:up_state_province] => DEVON\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Helen\n    [:up_first_name] => Helen\n    [:last_name] => Barlow\n    [:up_last_name] => Barlow\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979601\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T09:22:37+01:00\n    [:up_quote_created_time] => 2025-08-04T09:22:37+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 535\n    [:up_total_list_amount] => 535\n    [:total_net_amount] => 535\n    [:up_total_net_amount] => 535\n    [:total_amount] => 535\n    [:up_total_amount] => 535\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 2595\n    [:up_end_customer] => 2595\n    [:quote_contact] => 14922\n    [:up_quote_contact] => 14922\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-10-04\n    [:up_start_date] => 2025-10-04\n    [:end_date] => 2026-10-03\n    [:up_end_date] => 2026-10-03\n    [:offering_id] => OD-000280\n    [:up_offering_id] => OD-000280\n    [:offering_code] => RVTLTS\n    [:up_offering_code] => RVTLTS\n    [:offering_name] => AutoCAD Revit LT Suite\n    [:up_offering_name] => AutoCAD Revit LT Suite\n    [:marketing_name] => AutoCAD Revit LT Suite\n    [:up_marketing_name] => AutoCAD Revit LT Suite\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 535\n    [:up_unit_srp] => 535\n    [:extended_srp] => 535\n    [:up_extended_srp] => 535\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 535\n    [:up_end_user_price] => 535\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 57005128151617\n    [:up_subscription_id] => 57005128151617\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-10-03\n    [:up_subscription_endDate] => 2025-10-03\n    [:quote_id] => 1860\n    [:up_quote_id] => 1860\n)\n
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 08:22:43] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 08:23:32] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 08:23:32] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 08:23:32] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:23:32] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => GJR ARCHITECTS Ltd\n    [:up_name] => GJR ARCHITECTS Ltd\n    [:address1] => Axehayes Farm 1 Park 7 The Studio\n    [:up_address1] => Axehayes Farm 1 Park 7 The Studio\n    [:address2] => Clyst St. Mary\n    [:up_address2] => Clyst St. Mary\n    [:city] => Exeter\n    [:up_city] => Exeter\n    [:postal_code] => EX5 1DP\n    [:up_postal_code] => EX5 1DP\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => DEVON\n    [:up_state_province] => DEVON\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Helen\n    [:up_first_name] => Helen\n    [:last_name] => Barlow\n    [:up_last_name] => Barlow\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979608\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T09:23:26+01:00\n    [:up_quote_created_time] => 2025-08-04T09:23:26+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 2595\n    [:up_end_customer] => 2595\n    [:quote_contact] => 14922\n    [:up_quote_contact] => 14922\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-10-04\n    [:up_start_date] => 2025-10-04\n    [:end_date] => 2026-10-03\n    [:up_end_date] => 2026-10-03\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 57005125705871\n    [:up_subscription_id] => 57005125705871\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-10-03\n    [:up_subscription_endDate] => 2025-10-03\n    [:quote_id] => 1862\n    [:up_quote_id] => 1862\n)\n
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 08:23:33] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => PCC CONSULTANTS Ltd\n    [:up_name] => PCC CONSULTANTS Ltd\n    [:address1] => The Annexe 11 Meadow Lane\n    [:up_address1] => The Annexe 11 Meadow Lane\n    [:address2] => South Hykeham\n    [:up_address2] => South Hykeham\n    [:city] => Lincoln\n    [:up_city] => Lincoln\n    [:postal_code] => LN6 9PF\n    [:up_postal_code] => LN6 9PF\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Callum\n    [:up_first_name] => Callum\n    [:last_name] => Moseley\n    [:up_last_name] => Moseley\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979616\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T09:24:44+01:00\n    [:up_quote_created_time] => 2025-08-04T09:24:44+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 492\n    [:up_total_amount] => 492\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 82\n    [:up_estimated_tax] => 82\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 6066\n    [:up_end_customer] => 6066\n    [:quote_contact] => 18734\n    [:up_quote_contact] => 18734\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-11-02\n    [:up_start_date] => 2025-11-02\n    [:end_date] => 2026-11-01\n    [:up_end_date] => 2026-11-01\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 72829560655697\n    [:up_subscription_id] => 72829560655697\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-11-01\n    [:up_subscription_endDate] => 2025-11-01\n    [:quote_id] => 1864\n    [:up_quote_id] => 1864\n)\n
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 08:24:50] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Allwater Technologies\n    [:up_name] => Allwater Technologies\n    [:address1] => Cheddar Business Park Wedmore Road\n    [:up_address1] => Cheddar Business Park Wedmore Road\n    [:city] => Cheddar\n    [:up_city] => Cheddar\n    [:postal_code] => BS27 3EB\n    [:up_postal_code] => BS27 3EB\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => SOMERSET\n    [:up_state_province] => SOMERSET\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Kate\n    [:up_first_name] => Kate\n    [:last_name] => Wisniewski\n    [:up_last_name] => Wisniewski\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-979872\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T10:12:46+01:00\n    [:up_quote_created_time] => 2025-08-04T10:12:46+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1769\n    [:up_end_customer] => 1769\n    [:quote_contact] => 83166\n    [:up_quote_contact] => 83166\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-02\n    [:up_start_date] => 2025-09-02\n    [:end_date] => 2026-09-01\n    [:up_end_date] => 2026-09-01\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 56743625158902\n    [:up_subscription_id] => 56743625158902\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-01\n    [:up_subscription_endDate] => 2025-09-01\n    [:quote_id] => 1867\n    [:up_quote_id] => 1867\n)\n
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 09:12:52] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => MOSEDALE GILLATT ARCHITECTS\n    [:up_name] => MOSEDALE GILLATT ARCHITECTS\n    [:address1] => East Lodge\n    [:up_address1] => East Lodge\n    [:city] => Newcastle Upon Tyne\n    [:up_city] => Newcastle Upon Tyne\n    [:postal_code] => NE2 1PR\n    [:up_postal_code] => NE2 1PR\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Tim\n    [:up_first_name] => Tim\n    [:last_name] => Mosedale\n    [:up_last_name] => Mosedale\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-980363\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-04T12:14:56+01:00\n    [:up_quote_created_time] => 2025-08-04T12:14:56+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 2050\n    [:up_total_list_amount] => 2050\n    [:total_net_amount] => 2050\n    [:up_total_net_amount] => 2050\n    [:total_amount] => 2050\n    [:up_total_amount] => 2050\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3998\n    [:up_end_customer] => 3998\n    [:quote_contact] => 275448\n    [:up_quote_contact] => 275448\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 5\n    [:up_quantity] => 5\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-11-01\n    [:up_start_date] => 2025-11-01\n    [:end_date] => 2026-10-31\n    [:up_end_date] => 2026-10-31\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 2050\n    [:up_extended_srp] => 2050\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 2050\n    [:up_end_user_price] => 2050\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 66730475313679\n    [:up_subscription_id] => 66730475313679\n    [:subscription_quantity] => 5\n    [:up_subscription_quantity] => 5\n    [:subscription_endDate] => 2025-10-31\n    [:up_subscription_endDate] => 2025-10-31\n    [:quote_id] => 1873\n    [:up_quote_id] => 1873\n)\n
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-04 11:15:03] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-04 11:53:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-08-04 11:53:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => jba Ltd\n    [:up_name] => jba Ltd\n)\n
[data_importer] [2025-08-04 11:53:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-08-04 11:53:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 11:53:16] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-08-04 11:53:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, state_province = :state_province, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, portal_registration = :portal_registration, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, state_province = :up_state_province, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, portal_registration = :up_portal_registration, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-08-04 11:53:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => jba Ltd\n    [:up_name] => jba Ltd\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => Unit 17 Evolution Wynyard Avenue\n    [:up_address1] => Unit 17 Evolution Wynyard Avenue\n    [:address2] => Wynyard\n    [:up_address2] => Wynyard\n    [:city] => Billingham\n    [:up_city] => Billingham\n    [:state_province] => CLEVELAND\n    [:up_state_province] => CLEVELAND\n    [:postal_code] => TS22 5TB\n    [:up_postal_code] => TS22 5TB\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => \n    [:up_named_account_flag] => \n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => AEC\n    [:up_parent_industry_group] => AEC\n    [:parent_industry_segment] => Engineering Service Providers\n    [:up_parent_industry_segment] => Engineering Service Providers\n    [:primary_admin_first_name] => Paul\n    [:up_primary_admin_first_name] => Paul\n    [:primary_admin_last_name] => Cockrill\n    [:up_primary_admin_last_name] => Cockrill\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => 8120819\n    [:up_team_id] => 8120819\n    [:team_name] => Paul Cockrill - 0819\n    [:up_team_name] => Paul Cockrill - 0819\n    [:first_name] => Paul\n    [:up_first_name] => Paul\n    [:last_name] => Cockrill\n    [:up_last_name] => Cockrill\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:portal_registration] => Registered\n    [:up_portal_registration] => Registered\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => \n    [:up_do_not_email] => \n    [:do_not_mail] => 1\n    [:up_do_not_mail] => 1\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-04 11:53:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-08-04 11:53:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, opportunityNumber = :opportunityNumber, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, opportunityNumber = :up_opportunityNumber, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-08-04 11:53:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 75429970383863\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2025-08-04\n    [:up_startDate] => 2025-08-04\n    [:endDate] => 2025-12-14\n    [:up_endDate] => 2025-12-14\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000031\n    [:up_offeringId] => OD-000031\n    [:offeringCode] => ACDLT\n    [:up_offeringCode] => ACDLT\n    [:offeringName] => AutoCAD LT\n    [:up_offeringName] => AutoCAD LT\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:opportunityNumber] => **********\n    [:up_opportunityNumber] => **********\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => Credit Card\n    [:up_paymentMethod] => Credit Card\n    [:endCustomer_id] => 275451\n    [:up_endCustomer_id] => 275451\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 4565\n    [:up_soldTo_id] => 4565\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Fraser\n    [:up_first_name] => Fraser\n    [:last_name] => Dixon\n    [:up_last_name] => Dixon\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Martin Playford\n    [:up_name] => Martin Playford\n    [:address1] => 3 Roundle Avenue\n    [:up_address1] => 3 Roundle Avenue\n    [:city] => Bognor Regis\n    [:up_city] => Bognor Regis\n    [:postal_code] => PO22 8LQ\n    [:up_postal_code] => PO22 8LQ\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => 1\n    [:up_individual_flag] => 1\n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Martin\n    [:up_first_name] => Martin\n    [:last_name] => Playford\n    [:up_last_name] => Playford\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-983134\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-05T09:00:43+01:00\n    [:up_quote_created_time] => 2025-08-05T09:00:43+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 492\n    [:up_total_amount] => 492\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 82\n    [:up_estimated_tax] => 82\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 10553\n    [:up_end_customer] => 10553\n    [:quote_contact] => 275455\n    [:up_quote_contact] => 275455\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-08\n    [:up_start_date] => 2025-08-08\n    [:end_date] => 2026-08-07\n    [:up_end_date] => 2026-08-07\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 65995913110554\n    [:up_subscription_id] => 65995913110554\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-08-07\n    [:up_subscription_endDate] => 2025-08-07\n    [:quote_id] => 1879\n    [:up_quote_id] => 1879\n)\n
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-05 08:00:50] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-05 11:39:19] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name;
[data_importer] [2025-08-05 11:39:19] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Thermal Earth Ltd\n    [:up_name] => Thermal Earth Ltd\n)\n
[data_importer] [2025-08-05 11:39:19] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, account_type = :account_type, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country = :country, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country = :up_country, country_code = :up_country_code;
[data_importer] [2025-08-05 11:39:19] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:account_type] => Reseller\n    [:up_account_type] => Reseller\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-05 11:39:19] [data_importer.class.php:321] Skipping nurtureReseller - no valid data found
[data_importer] [2025-08-05 11:39:19] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET name = :name, account_type = :account_type, address1 = :address1, city = :city, postal_code = :postal_code, country = :country, individual_flag = :individual_flag, named_account_flag = :named_account_flag, named_account_group = :named_account_group, parent_industry_group = :parent_industry_group, parent_industry_segment = :parent_industry_segment, primary_admin_first_name = :primary_admin_first_name, primary_admin_last_name = :primary_admin_last_name, primary_admin_email = :primary_admin_email, team_id = :team_id, team_name = :team_name, first_name = :first_name, last_name = :last_name, email = :email, status = :status, do_not_call = :do_not_call, do_not_email = :do_not_email, do_not_mail = :do_not_mail, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, account_type = :up_account_type, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country = :up_country, individual_flag = :up_individual_flag, named_account_flag = :up_named_account_flag, named_account_group = :up_named_account_group, parent_industry_group = :up_parent_industry_group, parent_industry_segment = :up_parent_industry_segment, primary_admin_first_name = :up_primary_admin_first_name, primary_admin_last_name = :up_primary_admin_last_name, primary_admin_email = :up_primary_admin_email, team_id = :up_team_id, team_name = :up_team_name, first_name = :up_first_name, last_name = :up_last_name, email = :up_email, status = :up_status, do_not_call = :up_do_not_call, do_not_email = :up_do_not_email, do_not_mail = :up_do_not_mail, country_code = :up_country_code;
[data_importer] [2025-08-05 11:39:19] [data_importer.class.php:423] SQL Params: Array\n(\n    [:name] => Thermal Earth Ltd\n    [:up_name] => Thermal Earth Ltd\n    [:account_type] => End Customer\n    [:up_account_type] => End Customer\n    [:address1] => Unit B1 Capel Hendre Ind Est\n    [:up_address1] => Unit B1 Capel Hendre Ind Est\n    [:city] => Ammanford\n    [:up_city] => Ammanford\n    [:postal_code] => SA18 3SJ\n    [:up_postal_code] => SA18 3SJ\n    [:country] => United Kingdom\n    [:up_country] => United Kingdom\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:named_account_flag] => 1\n    [:up_named_account_flag] => 1\n    [:named_account_group] => Territory\n    [:up_named_account_group] => Territory\n    [:parent_industry_group] => MFG\n    [:up_parent_industry_group] => MFG\n    [:parent_industry_segment] => Industrial Machinery\n    [:up_parent_industry_segment] => Industrial Machinery\n    [:primary_admin_first_name] => Thermal\n    [:up_primary_admin_first_name] => Thermal\n    [:primary_admin_last_name] => Earth\n    [:up_primary_admin_last_name] => Earth\n    [:primary_admin_email] => <EMAIL>\n    [:up_primary_admin_email] => <EMAIL>\n    [:team_id] => ********\n    [:up_team_id] => ********\n    [:team_name] => Thermal Earth - 3483\n    [:up_team_name] => Thermal Earth - 3483\n    [:first_name] => Thermal\n    [:up_first_name] => Thermal\n    [:last_name] => Earth\n    [:up_last_name] => Earth\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:status] => Active\n    [:up_status] => Active\n    [:do_not_call] => \n    [:up_do_not_call] => \n    [:do_not_email] => \n    [:up_do_not_email] => \n    [:do_not_mail] => \n    [:up_do_not_mail] => \n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-05 11:39:19] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [nurtureReseller_id] => <group_insert_id>nurtureReseller</group_insert_id>\n    [endCustomer_id] => <group_insert_id>endCustomer</group_insert_id>\n    [solutionProvider_id] => <group_insert_id>solutionProvider</group_insert_id>\n    [soldTo_id] => <group_insert_id>soldTo</group_insert_id>\n    [nurtureReseller_csn] => accounts.nurtureReseller.csn\n    [endCustomer_csn] => endCustomer.account.endCustomerCsn\n    [solutionProvider_csn] => accounts.solutionProvider.csn\n    [soldTo_csn] => accounts.soldTo.csn\n)\n
[data_importer] [2025-08-05 11:39:19] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_subscriptions SET subscriptionId = :subscriptionId, quantity = :quantity, status = :status, startDate = :startDate, endDate = :endDate, term = :term, billingBehavior = :billingBehavior, billingFrequency = :billingFrequency, offeringId = :offeringId, offeringCode = :offeringCode, offeringName = :offeringName, autoRenew = :autoRenew, recordType = :recordType, intendedUsage = :intendedUsage, connectivity = :connectivity, connectivityInterval = :connectivityInterval, servicePlan = :servicePlan, accessModel = :accessModel, paymentMethod = :paymentMethod, endCustomer_id = :endCustomer_id, solutionProvider_id = :solutionProvider_id, soldTo_id = :soldTo_id, nurtureReseller_csn = :nurtureReseller_csn, endCustomer_csn = :endCustomer_csn, solutionProvider_csn = :solutionProvider_csn, soldTo_csn = :soldTo_csn ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, status = :up_status, startDate = :up_startDate, endDate = :up_endDate, term = :up_term, billingBehavior = :up_billingBehavior, billingFrequency = :up_billingFrequency, offeringId = :up_offeringId, offeringCode = :up_offeringCode, offeringName = :up_offeringName, autoRenew = :up_autoRenew, recordType = :up_recordType, intendedUsage = :up_intendedUsage, connectivity = :up_connectivity, connectivityInterval = :up_connectivityInterval, servicePlan = :up_servicePlan, accessModel = :up_accessModel, paymentMethod = :up_paymentMethod, endCustomer_id = :up_endCustomer_id, solutionProvider_id = :up_solutionProvider_id, soldTo_id = :up_soldTo_id, nurtureReseller_csn = :up_nurtureReseller_csn, endCustomer_csn = :up_endCustomer_csn, solutionProvider_csn = :up_solutionProvider_csn, soldTo_csn = :up_soldTo_csn;
[data_importer] [2025-08-05 11:39:19] [data_importer.class.php:423] SQL Params: Array\n(\n    [:subscriptionId] => 75438658522256\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:status] => Active\n    [:up_status] => Active\n    [:startDate] => 2025-08-05\n    [:up_startDate] => 2025-08-05\n    [:endDate] => 2026-08-04\n    [:up_endDate] => 2026-08-04\n    [:term] => Annual\n    [:up_term] => Annual\n    [:billingBehavior] => Recurring\n    [:up_billingBehavior] => Recurring\n    [:billingFrequency] => Annual\n    [:up_billingFrequency] => Annual\n    [:offeringId] => OD-000027\n    [:up_offeringId] => OD-000027\n    [:offeringCode] => ACDIST\n    [:up_offeringCode] => ACDIST\n    [:offeringName] => AutoCAD - including specialized toolsets\n    [:up_offeringName] => AutoCAD - including specialized toolsets\n    [:autoRenew] => ON\n    [:up_autoRenew] => ON\n    [:recordType] => Attribute based\n    [:up_recordType] => Attribute based\n    [:intendedUsage] => COM\n    [:up_intendedUsage] => COM\n    [:connectivity] => Online\n    [:up_connectivity] => Online\n    [:connectivityInterval] => 30 Days\n    [:up_connectivityInterval] => 30 Days\n    [:servicePlan] => Standard\n    [:up_servicePlan] => Standard\n    [:accessModel] => Single User\n    [:up_accessModel] => Single User\n    [:paymentMethod] => Credit Card\n    [:up_paymentMethod] => Credit Card\n    [:endCustomer_id] => 275458\n    [:up_endCustomer_id] => 275458\n    [:solutionProvider_id] => 2\n    [:up_solutionProvider_id] => 2\n    [:soldTo_id] => 6487\n    [:up_soldTo_id] => 6487\n    [:nurtureReseller_csn] => \n    [:up_nurtureReseller_csn] => \n    [:endCustomer_csn] => \n    [:up_endCustomer_csn] => \n    [:solutionProvider_csn] => \n    [:up_solutionProvider_csn] => \n    [:soldTo_csn] => \n    [:up_soldTo_csn] => \n)\n
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Rkb Electrical Ltd\n    [:up_name] => Rkb Electrical Ltd\n    [:address1] => Unit 16C Ghyll Way\n    [:up_address1] => Unit 16C Ghyll Way\n    [:address2] => Airedale Business Centre\n    [:up_address2] => Airedale Business Centre\n    [:city] => Skipton\n    [:up_city] => Skipton\n    [:postal_code] => BD23 2DD\n    [:up_postal_code] => BD23 2DD\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => NORTH YORKSHIRE\n    [:up_state_province] => NORTH YORKSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Rickerby\n    [:up_last_name] => Rickerby\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-983948\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-05T12:43:54+01:00\n    [:up_quote_created_time] => 2025-08-05T12:43:54+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 820\n    [:up_total_list_amount] => 820\n    [:total_net_amount] => 820\n    [:up_total_net_amount] => 820\n    [:total_amount] => 984\n    [:up_total_amount] => 984\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 164\n    [:up_estimated_tax] => 164\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 4004\n    [:up_end_customer] => 4004\n    [:quote_contact] => 275462\n    [:up_quote_contact] => 275462\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 2\n    [:up_quantity] => 2\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-06\n    [:up_start_date] => 2025-09-06\n    [:end_date] => 2026-09-05\n    [:up_end_date] => 2026-09-05\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 820\n    [:up_extended_srp] => 820\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 820\n    [:up_end_user_price] => 820\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 69400848050282\n    [:up_subscription_id] => 69400848050282\n    [:subscription_quantity] => 2\n    [:up_subscription_quantity] => 2\n    [:subscription_endDate] => 2025-09-05\n    [:up_subscription_endDate] => 2025-09-05\n    [:quote_id] => 1887\n    [:up_quote_id] => 1887\n)\n
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-05 11:44:00] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => RESYS ENGINEERING SERVICES Ltd\n    [:up_name] => RESYS ENGINEERING SERVICES Ltd\n    [:address1] => Unit 7, Oldmeldrum Business Centre\n    [:up_address1] => Unit 7, Oldmeldrum Business Centre\n    [:address2] => Colpy Way\n    [:up_address2] => Colpy Way\n    [:city] => Inverurie\n    [:up_city] => Inverurie\n    [:postal_code] => Ab51 0bz\n    [:up_postal_code] => Ab51 0bz\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => ABERDEENSHIRE\n    [:up_state_province] => ABERDEENSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Calum\n    [:up_first_name] => Calum\n    [:last_name] => Masson\n    [:up_last_name] => Masson\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-983952\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-05T12:45:03+01:00\n    [:up_quote_created_time] => 2025-08-05T12:45:03+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 820\n    [:up_total_list_amount] => 820\n    [:total_net_amount] => 820\n    [:up_total_net_amount] => 820\n    [:total_amount] => 984\n    [:up_total_amount] => 984\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 164\n    [:up_estimated_tax] => 164\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 10803\n    [:up_end_customer] => 10803\n    [:quote_contact] => 275466\n    [:up_quote_contact] => 275466\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 2\n    [:up_quantity] => 2\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-09\n    [:up_start_date] => 2025-08-09\n    [:end_date] => 2026-08-08\n    [:up_end_date] => 2026-08-08\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 820\n    [:up_extended_srp] => 820\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 820\n    [:up_end_user_price] => 820\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 55787120285135\n    [:up_subscription_id] => 55787120285135\n    [:subscription_quantity] => 2\n    [:up_subscription_quantity] => 2\n    [:subscription_endDate] => 2025-08-08\n    [:up_subscription_endDate] => 2025-08-08\n    [:quote_id] => 1889\n    [:up_quote_id] => 1889\n)\n
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-05 11:45:11] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Michael Eales Architects Ltd\n    [:up_name] => Michael Eales Architects Ltd\n    [:address1] => Unit 9 Park Lane Business\n    [:up_address1] => Unit 9 Park Lane Business\n    [:address2] => Centre Park Lane Langham\n    [:up_address2] => Centre Park Lane Langham\n    [:city] => Colchester\n    [:up_city] => Colchester\n    [:postal_code] => CO4 5WR\n    [:up_postal_code] => CO4 5WR\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => ESSEX\n    [:up_state_province] => ESSEX\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Michael\n    [:up_first_name] => Michael\n    [:last_name] => Eales\n    [:up_last_name] => Eales\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-984562\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-05T15:44:51+01:00\n    [:up_quote_created_time] => 2025-08-05T15:44:51+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 14375\n    [:up_total_list_amount] => 14375\n    [:total_net_amount] => 14375\n    [:up_total_net_amount] => 14375\n    [:total_amount] => 17250\n    [:up_total_amount] => 17250\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 2875\n    [:up_estimated_tax] => 2875\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 4036\n    [:up_end_customer] => 4036\n    [:quote_contact] => 157956\n    [:up_quote_contact] => 157956\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 5\n    [:up_quantity] => 5\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-13\n    [:up_start_date] => 2025-08-13\n    [:end_date] => 2026-08-12\n    [:up_end_date] => 2026-08-12\n    [:offering_id] => OD-000052\n    [:up_offering_id] => OD-000052\n    [:offering_code] => AECCOL\n    [:up_offering_code] => AECCOL\n    [:offering_name] => AEC Collection\n    [:up_offering_name] => AEC Collection\n    [:marketing_name] => Architecture Engineering & Construction Collection\n    [:up_marketing_name] => Architecture Engineering & Construction Collection\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 2875\n    [:up_unit_srp] => 2875\n    [:extended_srp] => 14375\n    [:up_extended_srp] => 14375\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 14375\n    [:up_end_user_price] => 14375\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 72359337050211\n    [:up_subscription_id] => 72359337050211\n    [:subscription_quantity] => 5\n    [:up_subscription_quantity] => 5\n    [:subscription_endDate] => 2025-08-12\n    [:up_subscription_endDate] => 2025-08-12\n    [:quote_id] => 1895\n    [:up_quote_id] => 1895\n)\n
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-05 14:44:58] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => SankeyCAD Ltd\n    [:up_name] => SankeyCAD Ltd\n    [:address1] => 25 Boston Boulevard\n    [:up_address1] => 25 Boston Boulevard\n    [:address2] => Great Sankey\n    [:up_address2] => Great Sankey\n    [:city] => Warrington\n    [:up_city] => Warrington\n    [:postal_code] => WA5 8HL\n    [:up_postal_code] => WA5 8HL\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => CHESHIRE\n    [:up_state_province] => CHESHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => stuart\n    [:up_first_name] => stuart\n    [:last_name] => Dooley\n    [:up_last_name] => Dooley\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-986742\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-06T10:16:45+01:00\n    [:up_quote_created_time] => 2025-08-06T10:16:45+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 492\n    [:up_total_amount] => 492\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 82\n    [:up_estimated_tax] => 82\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3019\n    [:up_end_customer] => 3019\n    [:quote_contact] => 275474\n    [:up_quote_contact] => 275474\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-10\n    [:up_start_date] => 2025-08-10\n    [:end_date] => 2026-08-09\n    [:up_end_date] => 2026-08-09\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 72068650045222\n    [:up_subscription_id] => 72068650045222\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-08-09\n    [:up_subscription_endDate] => 2025-08-09\n    [:quote_id] => 1903\n    [:up_quote_id] => 1903\n)\n
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-06 09:16:51] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => THUNDERBOLT & MAINTENANCE\n    [:up_name] => THUNDERBOLT & MAINTENANCE\n    [:address1] => Unit 2 Forum Road\n    [:up_address1] => Unit 2 Forum Road\n    [:city] => Nottingham\n    [:up_city] => Nottingham\n    [:postal_code] => NG5 9RW\n    [:up_postal_code] => NG5 9RW\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Stevenson\n    [:up_last_name] => Stevenson\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-987136\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-06T12:24:01+01:00\n    [:up_quote_created_time] => 2025-08-06T12:24:01+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 8608\n    [:up_end_customer] => 8608\n    [:quote_contact] => 275369\n    [:up_quote_contact] => 275369\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-14\n    [:up_start_date] => 2025-09-14\n    [:end_date] => 2026-09-13\n    [:up_end_date] => 2026-09-13\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 60007740489301\n    [:up_subscription_id] => 60007740489301\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-13\n    [:up_subscription_endDate] => 2025-09-13\n    [:quote_id] => 1905\n    [:up_quote_id] => 1905\n)\n
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-06 11:24:08] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Fraser\n    [:up_first_name] => Fraser\n    [:last_name] => Dixon\n    [:up_last_name] => Dixon\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => 247 INTERIORS\n    [:up_name] => 247 INTERIORS\n    [:address1] => Unit 31 Park Farm Industrial Estate\n    [:up_address1] => Unit 31 Park Farm Industrial Estate\n    [:address2] => Ermine Street\n    [:up_address2] => Ermine Street\n    [:city] => Buntingford\n    [:up_city] => Buntingford\n    [:postal_code] => SG9 9AZ\n    [:up_postal_code] => SG9 9AZ\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => HERTFORDSHIRE\n    [:up_state_province] => HERTFORDSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => jade\n    [:up_first_name] => jade\n    [:last_name] => major\n    [:up_last_name] => major\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-987314\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-06T13:28:10+01:00\n    [:up_quote_created_time] => 2025-08-06T13:28:10+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 4394\n    [:up_end_customer] => 4394\n    [:quote_contact] => 275482\n    [:up_quote_contact] => 275482\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-08-06 12:28:16] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => KOHA ARCHITECTS Ltd\n    [:up_name] => KOHA ARCHITECTS Ltd\n    [:address1] => Tremough Innovation Centre\n    [:up_address1] => Tremough Innovation Centre\n    [:city] => Penryn\n    [:up_city] => Penryn\n    [:postal_code] => TR10 9TA\n    [:up_postal_code] => TR10 9TA\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => CORNWALL\n    [:up_state_province] => CORNWALL\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Antony\n    [:up_first_name] => Antony\n    [:last_name] => Morvan\n    [:up_last_name] => Morvan\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-987799\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-06T15:17:57+01:00\n    [:up_quote_created_time] => 2025-08-06T15:17:57+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 5642\n    [:up_end_customer] => 5642\n    [:quote_contact] => 18178\n    [:up_quote_contact] => 18178\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-01\n    [:up_start_date] => 2025-09-01\n    [:end_date] => 2026-08-31\n    [:up_end_date] => 2026-08-31\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 69357707427003\n    [:up_subscription_id] => 69357707427003\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-08-31\n    [:up_subscription_endDate] => 2025-08-31\n    [:quote_id] => 1909\n    [:up_quote_id] => 1909\n)\n
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-06 14:18:03] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => NGP Architecture Ltd\n    [:up_name] => NGP Architecture Ltd\n    [:address1] => Federation House 222-224\n    [:up_address1] => Federation House 222-224\n    [:address2] => Queensferry Road\n    [:up_address2] => Queensferry Road\n    [:city] => Edinburgh\n    [:up_city] => Edinburgh\n    [:postal_code] => EH4 2BN\n    [:up_postal_code] => EH4 2BN\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => MIDLOTHIAN\n    [:up_state_province] => MIDLOTHIAN\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Chris\n    [:up_first_name] => Chris\n    [:last_name] => Gray\n    [:up_last_name] => Gray\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-987942\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-06T15:53:41+01:00\n    [:up_quote_created_time] => 2025-08-06T15:53:41+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 1230\n    [:up_total_list_amount] => 1230\n    [:total_net_amount] => 1230\n    [:up_total_net_amount] => 1230\n    [:total_amount] => 1230\n    [:up_total_amount] => 1230\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1490\n    [:up_end_customer] => 1490\n    [:quote_contact] => 17494\n    [:up_quote_contact] => 17494\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 3\n    [:up_quantity] => 3\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-12\n    [:up_start_date] => 2025-08-12\n    [:end_date] => 2026-08-11\n    [:up_end_date] => 2026-08-11\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 1230\n    [:up_extended_srp] => 1230\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 1230\n    [:up_end_user_price] => 1230\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 72346478936486\n    [:up_subscription_id] => 72346478936486\n    [:subscription_quantity] => 3\n    [:up_subscription_quantity] => 3\n    [:subscription_endDate] => 2025-08-11\n    [:up_subscription_endDate] => 2025-08-11\n    [:quote_id] => 1911\n    [:up_quote_id] => 1911\n)\n
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-06 14:53:48] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Ramsay McMichael Consulting\n    [:up_name] => Ramsay McMichael Consulting\n    [:address1] => Standard Bldg 102 Hope Street\n    [:up_address1] => Standard Bldg 102 Hope Street\n    [:city] => Glasgow\n    [:up_city] => Glasgow\n    [:postal_code] => G2 6PH\n    [:up_postal_code] => G2 6PH\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => LANARKSHIRE\n    [:up_state_province] => LANARKSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Suzanne\n    [:up_first_name] => Suzanne\n    [:last_name] => McMichael\n    [:up_last_name] => McMichael\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, quote_opportunity_number = :quote_opportunity_number, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, quote_opportunity_number = :up_quote_opportunity_number, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-987965\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-06T16:01:31+01:00\n    [:up_quote_created_time] => 2025-08-06T16:01:31+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:quote_opportunity_number] => A-********\n    [:up_quote_opportunity_number] => A-********\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 535\n    [:up_total_list_amount] => 535\n    [:total_net_amount] => 535\n    [:up_total_net_amount] => 535\n    [:total_amount] => 535\n    [:up_total_amount] => 535\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => [{"email":"<EMAIL>"}]\n    [:up_additional_recipients] => [{"email":"<EMAIL>"}]\n    [:end_customer] => 1773\n    [:up_end_customer] => 1773\n    [:quote_contact] => 275329\n    [:up_quote_contact] => 275329\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, quote_id = :up_quote_id;
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => New\n    [:up_action] => New\n    [:offering_id] => OD-000280\n    [:up_offering_id] => OD-000280\n    [:offering_code] => RVTLTS\n    [:up_offering_code] => RVTLTS\n    [:offering_name] => AutoCAD Revit LT Suite\n    [:up_offering_name] => AutoCAD Revit LT Suite\n    [:marketing_name] => AutoCAD Revit LT Suite\n    [:up_marketing_name] => AutoCAD Revit LT Suite\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 535\n    [:up_unit_srp] => 535\n    [:extended_srp] => 535\n    [:up_extended_srp] => 535\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 535\n    [:up_end_user_price] => 535\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:quote_id] => 1913\n    [:up_quote_id] => 1913\n)\n
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-06 15:01:38] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Ramsay McMichael Consulting\n    [:up_name] => Ramsay McMichael Consulting\n    [:address1] => Standard Bldg 102 Hope Street\n    [:up_address1] => Standard Bldg 102 Hope Street\n    [:city] => Glasgow\n    [:up_city] => Glasgow\n    [:postal_code] => G2 6PH\n    [:up_postal_code] => G2 6PH\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => LANARKSHIRE\n    [:up_state_province] => LANARKSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => John\n    [:up_first_name] => John\n    [:last_name] => Stewart\n    [:up_last_name] => Stewart\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-987997\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-06T16:10:09+01:00\n    [:up_quote_created_time] => 2025-08-06T16:10:09+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1773\n    [:up_end_customer] => 1773\n    [:quote_contact] => 14074\n    [:up_quote_contact] => 14074\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-02\n    [:up_start_date] => 2025-09-02\n    [:end_date] => 2026-09-01\n    [:up_end_date] => 2026-09-01\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 72527130055219\n    [:up_subscription_id] => 72527130055219\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-01\n    [:up_subscription_endDate] => 2025-09-01\n    [:quote_id] => 1916\n    [:up_quote_id] => 1916\n)\n
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-06 15:10:16] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Aurangzaib\n    [:up_first_name] => Aurangzaib\n    [:last_name] => Mahmood\n    [:up_last_name] => Mahmood\n    [:phone] => +***********\n    [:up_phone] => +***********\n)\n
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => Ramsay McMichael Consulting\n    [:up_name] => Ramsay McMichael Consulting\n    [:address1] => Standard Bldg 102 Hope Street\n    [:up_address1] => Standard Bldg 102 Hope Street\n    [:city] => Glasgow\n    [:up_city] => Glasgow\n    [:postal_code] => G2 6PH\n    [:up_postal_code] => G2 6PH\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => LANARKSHIRE\n    [:up_state_province] => LANARKSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Suzanne\n    [:up_first_name] => Suzanne\n    [:last_name] => McMichael\n    [:up_last_name] => McMichael\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-988001\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-06T16:11:17+01:00\n    [:up_quote_created_time] => 2025-08-06T16:11:17+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 1773\n    [:up_end_customer] => 1773\n    [:quote_contact] => 275329\n    [:up_quote_contact] => 275329\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-08-06 15:11:23] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => MOBIUS STUDIO Ltd\n    [:up_name] => MOBIUS STUDIO Ltd\n    [:address1] => 13a St. Pauls Square\n    [:up_address1] => 13a St. Pauls Square\n    [:city] => Birmingham\n    [:up_city] => Birmingham\n    [:postal_code] => B3 1RB\n    [:up_postal_code] => B3 1RB\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Jason\n    [:up_first_name] => Jason\n    [:last_name] => Dodd\n    [:up_last_name] => Dodd\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-988019\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-06T16:16:17+01:00\n    [:up_quote_created_time] => 2025-08-06T16:16:17+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 275505\n    [:up_end_customer] => 275505\n    [:quote_contact] => 275506\n    [:up_quote_contact] => 275506\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-09-20\n    [:up_start_date] => 2025-09-20\n    [:end_date] => 2026-09-19\n    [:up_end_date] => 2026-09-19\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 59800188847283\n    [:up_subscription_id] => 59800188847283\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-09-19\n    [:up_subscription_endDate] => 2025-09-19\n    [:quote_id] => 1919\n    [:up_quote_id] => 1919\n)\n
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-06 15:16:24] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-06 15:47:50] [data_importer.class.php:1426] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sketchup32_csv_file_1754495269.csv
[data_importer] [2025-08-06 15:47:50] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sketchup32_csv_file_1754495269.csv
[data_importer] [2025-08-06 19:48:06] [data_importer.class.php:1426] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sketch2_csv_file_1754509685.csv
[data_importer] [2025-08-06 19:48:06] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sketch2_csv_file_1754509685.csv
[data_importer] [2025-08-06 19:58:11] [data_importer.class.php:1426] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//test1_csv_file_1754510290.csv
[data_importer] [2025-08-06 19:58:11] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//test1_csv_file_1754510290.csv
[data_importer] [2025-08-07 08:00:58] [data_importer.class.php:1426] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sketchup_test_csv_file_1754553657.csv
[data_importer] [2025-08-07 08:00:58] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sketchup_test_csv_file_1754553657.csv
[data_importer] [2025-08-07 08:10:16] [data_importer.class.php:1426] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//test55_csv_file_1754554215.csv
[data_importer] [2025-08-07 08:10:16] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//test55_csv_file_1754554215.csv
[data_importer] [2025-08-07 08:17:24] [data_importer.class.php:1426] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//test56_csv_file_1754554643.csv
[data_importer] [2025-08-07 08:17:24] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//test56_csv_file_1754554643.csv
[data_importer] [2025-08-07 08:18:34] [data_importer.class.php:1426] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sart_csv_file_1754554713.csv
[data_importer] [2025-08-07 08:18:34] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sart_csv_file_1754554713.csv
[data_importer] [2025-08-07 08:21:56] [data_importer.class.php:1426] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//test_67_csv_file_1754554915.csv
[data_importer] [2025-08-07 08:21:56] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//test_67_csv_file_1754554915.csv
[data_importer] [2025-08-07 08:27:43] [data_importer.class.php:1426] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/testimport_csv_file_1754555262.csv
[data_importer] [2025-08-07 08:27:43] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/testimport_csv_file_1754555262.csv
[data_importer] [2025-08-07 08:33:30] [data_importer.class.php:1381] import_csv_to_hilt_table called with table: autobooks_test0933_data, is_file_path: true
[data_importer] [2025-08-07 08:33:30] [data_importer.class.php:1393] Using file path: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0933_csv_file_1754555609.csv
[data_importer] [2025-08-07 08:33:30] [data_importer.class.php:1397] Calling import_csv_with_auto_schema
[data_importer] [2025-08-07 08:33:30] [data_importer.class.php:1431] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0933_csv_file_1754555609.csv
[data_importer] [2025-08-07 08:33:30] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0933_csv_file_1754555609.csv
[data_importer] [2025-08-07 08:36:57] [data_importer.class.php:1381] import_csv_to_hilt_table called with table: autobooks_test0936_data, is_file_path: true
[data_importer] [2025-08-07 08:36:57] [data_importer.class.php:1393] Using file path: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0936_csv_file_1754555816.csv
[data_importer] [2025-08-07 08:36:57] [data_importer.class.php:1397] Calling import_csv_with_auto_schema
[data_importer] [2025-08-07 08:36:57] [data_importer.class.php:1431] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0936_csv_file_1754555816.csv
[data_importer] [2025-08-07 08:36:57] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0936_csv_file_1754555816.csv
[data_importer] [2025-08-07 08:38:08] [data_importer.class.php:1381] import_csv_to_hilt_table called with table: autobooks_test0937_data, is_file_path: true
[data_importer] [2025-08-07 08:38:08] [data_importer.class.php:1393] Using file path: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0937_csv_file_1754555887.csv
[data_importer] [2025-08-07 08:38:08] [data_importer.class.php:1397] Calling import_csv_with_auto_schema
[data_importer] [2025-08-07 08:38:08] [data_importer.class.php:1431] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0937_csv_file_1754555887.csv
[data_importer] [2025-08-07 08:38:08] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0937_csv_file_1754555887.csv
[data_importer] [2025-08-07 08:44:42] [data_importer.class.php:1381] import_csv_to_hilt_table called with table: autobooks_test0944_data, is_file_path: true
[data_importer] [2025-08-07 08:44:42] [data_importer.class.php:1393] Using file path: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0944_csv_file_1754556281.csv
[data_importer] [2025-08-07 08:44:42] [data_importer.class.php:1397] Calling import_csv_with_auto_schema for table: autobooks_test0944_data
[data_importer] [2025-08-07 08:44:42] [data_importer.class.php:1432] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0944_csv_file_1754556281.csv
[data_importer] [2025-08-07 08:44:42] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/test0944_csv_file_1754556281.csv
[data_importer] [2025-08-07 08:44:42] [data_importer.class.php:1438] CSV analysis completed successfully
[data_importer] [2025-08-07 08:44:42] [data_importer.class.php:1441] Generating enhanced schema for table: autobooks_test0944_data
