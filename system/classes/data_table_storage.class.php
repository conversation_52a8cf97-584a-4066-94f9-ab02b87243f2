<?php
use system\database;
/**
 * Data Table Storage Class
 * Handles database operations for data table configurations
 */
class data_table_storage {
    
    /**
     * Get data table configuration from database
     * 
     * @param string $table_name The table identifier
     * @param int|null $user_id User ID (null for global config)
     * @return array|null Configuration array or null if not found
     */
    public static function get_configuration(string $table_name, ?int $user_id = null): ?array {
        try {
            $query = database::table('autobooks_data_table_storage')
                ->select(['configuration', 'data_source_id', 'updated_at'])
                ->where('table_name', $table_name);

            if ($user_id) {
                $query->where('user_id', $user_id);
            } else {
                $query->where('user_id', null);
            }

            $result = $query->first();

            if ($result) {
                return [
                    'configuration' => json_decode($result['configuration'], true) ?? [],
                    'data_source_id' => $result['data_source_id'],
                    'updated_at' => $result['updated_at']
                ];
            }

            return null;
        } catch (Exception $e) {
            // Handle error gracefully
            return null;
        }
    }

    /**
     * Save data table configuration to database
     *
     * @param string $table_name The table identifier
     * @param array $configuration Configuration array
     * @param int|null $user_id User ID (null for global config)
     * @param int|null $data_source_id Data source identifier
     * @return bool Success status
     */
    public static function save_configuration(string $table_name, array $configuration, ?int $user_id = null, ?int $data_source_id = null): bool {
        try {
            print_rr($configuration,'$configurationsavvy');
            // Check if configuration exists
            $query = database::table('autobooks_data_table_storage')
                ->where('table_name', $table_name);

            if ($user_id) {
                $query->where('user_id', $user_id);
            } else {
                $query->where('user_id', null);
            }

            $existing = $query->first();

            $data = [
                'table_name' => $table_name,
                'user_id' => $user_id,
                'configuration' => json_encode($configuration),
                'data_source_id' => $data_source_id
            ];

            // Debug output
            tcs_log(['configuration' => $configuration, 'table_name' => $table_name, 'data_source_id'=>$data_source_id], 'data_table_saga');



            if ($existing) {
                // Update existing record
                $update_query = database::table('autobooks_data_table_storage')
                    ->where('table_name', $table_name);

                if ($user_id) {
                    $update_query->where('user_id', $user_id);
                } else {
                    $update_query->where('user_id', null);
                }

                $update_query->update([
                    'configuration' => json_encode($configuration),
                    'data_source_id' => $data_source_id,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                // Insert new record
                database::table('autobooks_data_table_storage')->insert($data);
            }

            return true;
        } catch (Exception $e) {
            print_rr($e->getMessage(),'data_table_storage_save_error');
            return false;
        }
    }

    /**
     * Delete data table configuration
     *
     * @param string $table_name The table identifier
     * @param int|null $user_id User ID (null for global config)
     * @return bool Success status
     */
    public static function delete_configuration(string $table_name, ?int $user_id = null): bool {
        try {
            $query = database::table('autobooks_data_table_storage')
                ->where('table_name', $table_name);

            if ($user_id) {
                $query->where('user_id', $user_id);
            } else {
                $query->where('user_id', null);
            }

            $query->delete();
            return true;
        } catch (Exception $e) {
            // Handle error gracefully
            return false;
        }
    }

    /**
     * Initialize default configuration from provided data
     *
     * @param string $table_name The table identifier
     * @param array $columns Original columns array
     * @param int|null $user_id User ID (null for global config)
     * @param string|null $data_source_id Data source identifier
     * @return array The initialized configuration
     */
    public static function initialize_default_configuration(string $table_name, array $columns, ?int $user_id = null, ?int $data_source_id = null): array {
        $structure = [];
        $hidden = [];

        foreach ($columns as $index => $col) {
            $column_id = 'col_' . $index . '_' . md5($col['label']);
            $structure[] = [
                'id' => $column_id,
                'label' => $col['label'],
                'field' => $col['field'],
                'filter' => $col['filter'] ?? false,
                'fields' => is_array($col['field']) ? $col['field'] : [$col['field']],
                'visible' => true
            ];
        }

        // Check if there's an existing configuration to preserve data source settings
        $existing_config = self::get_configuration($table_name, $user_id);
        $existing_data_source_type = 'hardcoded';
        $existing_data_source_id = null;

        if ($existing_config) {
            $existing_data_source_type = $existing_config['configuration']['data_source_type'] ?? 'hardcoded';
            $existing_data_source_id = $existing_config['data_source_id'] ?? $existing_config['configuration']['data_source_id'] ?? null;
        }

        $configuration = [
            'hidden' => $hidden,
            'structure' => $structure,
            'columns' => $columns, // Store original columns for reference
            'data_source_type' => $existing_data_source_type, // Preserve existing data source type
            'data_source_id' => $existing_data_source_id, // Preserve existing data source ID
            'created_at' => date('Y-m-d H:i:s')
        ];

        // Use the existing data source ID if available, otherwise use the passed parameter
        $save_data_source_id = $existing_data_source_id ?? $data_source_id;

        // Save to database
        self::save_configuration($table_name, $configuration, $user_id, $save_data_source_id);

        return $configuration;
    }

    /**
     * Get or create configuration
     * Returns existing configuration or creates default from provided data
     *
     * @param string $table_name The table identifier
     * @param array $columns Original columns array (used for default creation)
     * @param int|null $user_id User ID (null for global config)
     * @param string|null $data_source_id Data source identifier
     * @return array Configuration array
     */
    public static function get_or_create_configuration(string $table_name, array $columns = [], ?int $user_id = null, ?string $data_source_id = null): array {
        $stored = self::get_configuration($table_name, $user_id);

        if ($stored) {
            return $stored['configuration'];
        }

        // If no columns provided, return blank configuration
        if (empty($columns)) {
            return [
                'hidden' => [],
                'structure' => [],
                'columns' => [],
                'data_source_type' => 'hardcoded',
                'created_at' => date('Y-m-d H:i:s')
            ];
        }

        // Initialize default configuration
        return self::initialize_default_configuration($table_name, $columns, $user_id, $data_source_id);
    }

    /**
     * Get data for a table configuration, either from hardcoded data or data source
     *
     * @param string $table_name The table identifier
     * @param array $hardcoded_data Fallback hardcoded data
     * @param array $criteria Additional criteria for data source queries
     * @param int|null $user_id User ID (null for global config)
     * @return array Data result with success status
     */
    public static function get_table_data(string $table_name, array $hardcoded_data = [], array $criteria = [], ?int $user_id = null): array {
        try {
            $config = self::get_configuration($table_name, $user_id);

            if (!$config) {
                // No configuration exists, return hardcoded data
                return [
                    'success' => true,
                    'data' => $hardcoded_data,
                    'source' => 'hardcoded',
                    'count' => count($hardcoded_data),
                    'total_count' => count($hardcoded_data)
                ];
            }

            $configuration = $config['configuration'];
            $data_source_type = $configuration['data_source_type'] ?? 'hardcoded';
            $data_source_id = $config['data_source_id'] ?? $configuration['data_source_id'] ?? null;

            // Debug output
            tcs_log("Getting table data: table=$table_name, type=$data_source_type, id=$data_source_id", 'data_table_saga');

            if ($data_source_type === 'data_source' && $data_source_id) {
                // Use data source
                $data_source_result = \system\data_source_manager::get_data_source_data($data_source_id, $criteria);
                tcs_log('Using data source: ' . $data_source_id, 'data_table_saga');
                if ($data_source_result['success']) {
//                    tcs_log('data_source_result: ' . print_r([
//                            'success' => true,
//                            'data' => $data_source_result['data'],
//                            'source' => 'data_source',
//                            'data_source_id' => $data_source_id,
//                            'count' => $data_source_result['count'],
//                            'total_count' => $data_source_result['total_count'] ?? $data_source_result['count'],
//                            'criteria' => $criteria,
//                            'query' => $data_source_result['query'] ?? null], true), 'data_table_saga');
                    return [
                        'success' => true,
                        'data' => $data_source_result['data'],
                        'config' => $configuration,
                        'source' => 'data_source',
                        'columns' => $data_source_result['columns'] ?? [],
                        'data_source_id' => $data_source_id,
                        'count' => $data_source_result['count'],
                        'total_count' => $data_source_result['total_count'] ?? $data_source_result['count'],
                        'criteria' => $criteria,
                        'query' => $data_source_result['query'] ?? null
                    ];
                } else {
                    // Data source failed, fall back to hardcoded
                    tcs_log('Data source failed, falling back to hardcoded data: ' . ($data_source_result['error'] ?? 'Unknown error'), 'data_table_saga');
                    return [
                        'success' => true,
                        'data' => $hardcoded_data,
                        'source' => 'hardcoded_fallback',
                        'error' => $data_source_result['error'] ?? 'Data source failed',
                        'count' => count($hardcoded_data),
                        'total_count' => count($hardcoded_data)
                    ];
                }
            } else {
                // Use hardcoded data
                tcs_log('Using hardcoded data', 'data_table_saga');
                return [
                    'success' => true,
                    'data' => $hardcoded_data,
                    'source' => 'hardcoded',
                    'count' => count($hardcoded_data),
                    'total_count' => count($hardcoded_data)
                ];
            }

        } catch (Exception $e) {
            // Error occurred, fall back to hardcoded data
            return [
                'success' => true,
                'data' => $hardcoded_data,
                'source' => 'hardcoded_fallback',
                'error' => $e->getMessage(),
                'count' => count($hardcoded_data)
            ];
        }
    }

    /**
     * Get current user ID from session/auth
     *
     * @return int|null User ID or null if not authenticated
     */
    public static function get_current_user_id(): ?int {
        // Check if users class exists and user is authenticated
        if (class_exists('users')) {
            $user = users::checkAuth();
            return $user['id'] ?? null;
        }

        // Fallback to session if available
        return $_SESSION['user_id'] ?? null;
    }

    /**
     * List all configurations for a user or globally
     *
     * @param int|null $user_id User ID (null for global configs)
     * @return array Array of configurations
     */
    public static function list_configurations(?int $user_id = null): array {
        try {
            $query = database::table('autobooks_data_table_storage')
                ->select(['table_name', 'data_source_id', 'updated_at']);

            if ($user_id) {
                $query->where('user_id', $user_id);
            } else {
                $query->where('user_id', null);
            }

            $results = $query->orderBy('updated_at', 'DESC')->get();

            $configurations = [];
            foreach ($results as $row) {
                $configurations[] = [
                    'table_name' => $row['table_name'],
                    'data_source_id' => $row['data_source_id'],
                    'updated_at' => $row['updated_at']
                ];
            }

            return $configurations;
        } catch (Exception $e) {
            // Handle error gracefully
            return [];
        }
    }
}
